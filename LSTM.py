import pandas as pd
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error, mean_absolute_percentage_error
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 定义LSTM模型
class LSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size=64, num_layers=2, dropout=0.2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM层
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers,
                           batch_first=True, dropout=dropout)

        # 全连接层
        self.fc = nn.Linear(hidden_size, 1)
        self.dropout = nn.Dropout(dropout)

    def forward(self, x):
        # 初始化隐藏状态
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)

        # LSTM前向传播
        out, _ = self.lstm(x, (h0, c0))

        # 取最后一个时间步的输出
        out = self.dropout(out[:, -1, :])
        out = self.fc(out)

        return out
# 加载和预处理数据
def load_and_preprocess_data():
    print("正在加载数据...")
    df = pd.read_excel('B0B41X9SMT.xlsx')

    # 确保日期列是datetime类型并排序
    df['日期'] = pd.to_datetime(df['日期'])
    df = df.sort_values('日期').reset_index(drop=True)

    print(f"数据总量: {len(df)} 天")
    print(f"时间范围: {df['日期'].min()} 到 {df['日期'].max()}")

    # 选择特征变量 - 选择与销量相关的重要特征
    feature_columns = [
        '销量',         
        '销售额',      
        '订单量',    
        '真实库存',      
        'Sessions 总计',  
        'PV总计',      
        '广告花费',     
        '广告销售额',   
        '点击量',      
        '展示量',   
        '评论数',   
        '评分',  
        '平均售价',
    ]

    # 检查列是否存在并处理缺失值
    available_columns = []
    for col in feature_columns:
        if col in df.columns:
            available_columns.append(col)
        else:
            print(f"警告: 列 '{col}' 不存在")

    # 选择可用的特征
    df_features = df[available_columns].copy()

    # 处理缺失值 - 用前值填充，如果还有缺失则用0填充
    df_features = df_features.fillna(method='ffill').fillna(0)

    print(f"使用的特征列: {available_columns}")
    print(f"特征数量: {len(available_columns)}")

    return df_features, df['日期']

# 创建时间序列数据
def create_sequences(data, target_col_idx, sequence_length=30):
    sequences = []
    targets = []

    for i in range(sequence_length, len(data)):
        # 输入序列 (过去sequence_length天的所有特征)
        seq = data[i-sequence_length:i]
        sequences.append(seq)

        # 目标值 (当前天的销量)
        target = data[i, target_col_idx]  # 销量是第0列
        targets.append(target)

    return np.array(sequences), np.array(targets)

# 准备训练和测试数据，确保无数据泄漏
def prepare_train_test_data(df_features, test_days=70, sequence_length=30):
    print(f"\n准备训练和测试数据...")
    print(f"测试集天数: {test_days}")
    print(f"序列长度: {sequence_length}")

    # 转换为numpy数组
    data = df_features.values.astype(np.float32)

    # 严格分割：训练数据不包含最后70天
    train_data = data[:-test_days]
    test_data = data  # 测试时需要用到历史数据来构建序列

    print(f"原始数据长度: {len(data)}")
    print(f"训练数据长度: {len(train_data)}")
    print(f"测试数据长度: {len(test_data)}")

    # 使用训练数据拟合标准化器
    scaler = MinMaxScaler()
    train_data_scaled = scaler.fit_transform(train_data)

    # 对完整数据进行标准化（用于构建测试序列）
    data_scaled = scaler.transform(data)

    # 创建训练序列 - 只使用训练数据
    X_train, y_train = create_sequences(train_data_scaled, 0, sequence_length)

    # 创建测试序列 - 使用滚动预测确保无数据泄漏
    X_test = []
    y_test = []

    # 获取最后70天的真实值（仅用于评估，不用于预测）
    test_start_idx = len(data) - test_days
    for i in range(test_start_idx, len(data)):
        y_test.append(data_scaled[i, 0])  # 真实销量值

    # 为滚动预测准备初始序列（使用训练数据的最后30天）
    initial_sequence = data_scaled[len(train_data)-sequence_length:len(train_data)]

    print(f"初始序列形状: {initial_sequence.shape}")
    print(f"将进行{test_days}步滚动预测")

    y_test = np.array(y_test)

    print(f"训练序列形状: {X_train.shape}")
    print(f"训练目标形状: {y_train.shape}")
    print(f"测试目标形状: {y_test.shape}")

    return X_train, y_train, initial_sequence, y_test, scaler

# 训练LSTM模型
def train_model(X_train, y_train, initial_sequence, y_test, input_size, epochs=100, batch_size=32, learning_rate=0.001):
    print(f"\n开始训练模型...")
    print(f"输入特征数: {input_size}")
    print(f"训练轮数: {epochs}")
    print(f"批次大小: {batch_size}")

    # 转换为PyTorch张量
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train).unsqueeze(1)

    # 创建数据加载器
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)

    # 初始化模型
    model = LSTMModel(input_size)
    criterion = nn.MSELoss()
    optimizer = torch.optim.Adam(model.parameters(), lr=learning_rate)

    # 训练循环
    train_losses = []

    for epoch in range(epochs):
        model.train()
        epoch_loss = 0

        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X)
            loss = criterion(outputs, batch_y)
            loss.backward()
            optimizer.step()
            epoch_loss += loss.item()

        avg_loss = epoch_loss / len(train_loader)
        train_losses.append(avg_loss)

        if (epoch + 1) % 20 == 0:
            print(f'Epoch [{epoch+1}/{epochs}], Loss: {avg_loss:.6f}')

    print("模型训练完成!")

    # 训练集预测
    model.eval()
    with torch.no_grad():
        train_pred = model(X_train_tensor).numpy()

    # 滚动预测测试集
    print("\n开始滚动预测...")
    test_pred = []
    current_sequence = initial_sequence.copy()  # 使用训练数据的最后30天作为初始序列

    for step in range(len(y_test)):
        # 将当前序列转换为张量并预测下一个值
        seq_tensor = torch.FloatTensor(current_sequence).unsqueeze(0)  # 添加batch维度

        with torch.no_grad():
            pred = model(seq_tensor).numpy()[0, 0]  # 获取预测值
            test_pred.append(pred)

        # 更新序列：移除第一个时间步，添加预测值
        # 注意：这里我们只更新销量特征（第0列），其他特征保持最后已知值
        new_step = current_sequence[-1].copy()  # 复制最后一步的所有特征
        new_step[0] = pred  # 更新销量预测值

        # 滚动更新序列
        current_sequence = np.vstack([current_sequence[1:], new_step.reshape(1, -1)])

        if (step + 1) % 10 == 0:
            print(f"已完成 {step + 1}/{len(y_test)} 步预测")

    test_pred = np.array(test_pred).reshape(-1, 1)
    print(f"滚动预测完成！预测了 {len(test_pred)} 个时间步")

    return model, train_pred, test_pred, train_losses

# 评估模型性能
def evaluate_model(y_true, y_pred, scaler, data_type=""):
    # 反标准化
    y_true_original = scaler.inverse_transform(
        np.column_stack([y_true] + [np.zeros(len(y_true)) for _ in range(scaler.n_features_in_-1)])
    )[:, 0]

    y_pred_original = scaler.inverse_transform(
        np.column_stack([y_pred.flatten()] + [np.zeros(len(y_pred)) for _ in range(scaler.n_features_in_-1)])
    )[:, 0]

    # 计算评估指标
    mse = mean_squared_error(y_true_original, y_pred_original)
    mape = mean_absolute_percentage_error(y_true_original, y_pred_original) * 100

    print(f"\n{data_type}评估结果:")
    print(f"MSE (均方误差): {mse:.2f}")
    print(f"MAPE (平均绝对百分比误差): {mape:.2f}%")

    return mse, mape, y_true_original, y_pred_original

def plot_results(dates, y_true, y_pred, title="销量预测结果对比"):
    plt.figure(figsize=(15, 8))

    plt.plot(dates, y_true, label='实际销量', color='blue', linewidth=2, marker='o', markersize=4)
    plt.plot(dates, y_pred, label='预测销量', color='red', linewidth=2, marker='s', markersize=4)

    plt.title(title, fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('销量', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()

    # 保存图片
    plt.savefig('LSTM_销量预测结果.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    print("基于LSTM的亚马逊销售预测模型")

    # 1. 加载和预处理数据
    df_features, dates = load_and_preprocess_data()

    # 2. 准备训练和测试数据
    X_train, y_train, initial_sequence, y_test, scaler = prepare_train_test_data(df_features)

    # 3. 训练模型
    input_size = X_train.shape[2]  # 特征数量
    model, train_pred, test_pred, train_losses = train_model(
        X_train, y_train, initial_sequence, y_test, input_size
    )

    # 4. 评估模型
    print("\n模型评估结果")

    # 训练集评估
    train_mse, train_mape, y_train_orig, train_pred_orig = evaluate_model(
        y_train, train_pred, scaler, "训练集"
    )

    # 测试集评估（最后70天）
    test_mse, test_mape, y_test_orig, test_pred_orig = evaluate_model(
        y_test, test_pred, scaler, "测试集（最后70天）"
    )

    # 5. 绘制结果
    test_dates = dates.iloc[-70:].reset_index(drop=True)
    plot_results(test_dates, y_test_orig, test_pred_orig,
                "LSTM模型 - 最后70天销量预测结果对比（滚动预测，无数据泄漏）")

    # 6. 输出最终结果摘要
    print(f"  MSE (均方误差): {test_mse:.2f}")
    print(f"  MAPE (平均绝对百分比误差): {test_mape:.2f}%")

    print("\n模型训练完成！预测结果图表已保存为 'LSTM_销量预测结果.png'")

if __name__ == "__main__":
    main()