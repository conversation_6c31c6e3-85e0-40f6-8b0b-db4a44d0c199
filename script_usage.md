# 亚马逊电商运营数据分析脚本使用说明

## 概述

这是一个专为亚马逊电商运营打造的自动化数据分析脚本，主要功能包括：
- 获取店铺列表信息
- 查询店铺信息（通过店铺名或SID）
- 获取产品表现数据
- 生成Excel报表
- 创建交互式断货曲线分析图

## 环境要求

### Python版本
- Python 3.7+

### 依赖包
```bash
pip install httpx pandas xlsxwriter plotly anyio pycryptodome
```

### API配置
脚本使用领星API，需要配置：
- API Host: `https://openapi.lingxing.com`
- App ID: `ak_MJOgYw9POgBN8`
- App Secret: `/FlD6FYxpwT7IKSu/yKFIg==`
- 代理服务器: `http://alfa:<EMAIL>:3128`

## 使用方法

### 启动脚本
```bash
python script.py
```

### 主菜单选项

脚本启动后会显示主菜单，提供以下选项：

#### 1. 查询店铺SID（输入1）
- 通过店铺名称查询对应的SID
- 输入店铺名称，系统会在店铺列表中查找匹配项
- 显示店铺的SID和在Excel文件中的位置

#### 2. 通过SID查询店铺（输入2）
- 通过SID反向查询店铺信息
- 输入SID，系统会显示对应的店铺名称和位置信息

#### 3. 获取产品表现数据（输入3）
这是脚本的核心功能，包含完整的数据获取和分析流程：

**输入参数：**
- SID：店铺标识符
- 起始日期：格式为YYYY-MM-DD
- 结束日期：格式为YYYY-MM-DD

**处理流程：**
1. 验证SID和日期格式
2. 检查缺失的数据日期
3. 从API获取产品表现数据
4. 生成Excel报表
5. 创建交互式断货曲线图

#### Q. 退出程序
输入Q退出脚本

## 数据结构

### 文件夹结构
```
工作目录/
├── 店铺列表/
│   ├── 店铺列表.json
│   └── 店铺列表.xlsx
└── [店铺名]/
    └── 产品表现/
        ├── json/           # 原始JSON数据
        ├── excel/          # Excel报表
        └── html/           # 交互式图表
```

### Excel报表字段

报表包含以下主要数据类别：

**基础信息：**
- 日期、ASIN、父ASIN、产品标题、品牌、分类等

**销售数据：**
- 销量、销售额、订单量及其环比数据
- B2B销售数据
- 促销数据

**利润数据：**
- 结算毛利润、订单毛利润、毛利率、ROI等

**库存信息：**
- FBA可售、在途、不可售库存
- 真实库存（计算字段）
- 可售天数预估

**广告数据：**
- 各类广告费用和销售额
- 点击量、展示量、转化率等指标

**流量数据：**
- Sessions、PV、CVR、Buybox等

**评价和退货：**
- 评论数、评分、留评率
- 退款率、退货率等

## 特殊功能

### 1. 增量数据获取
- 脚本会自动检查已有数据，只下载缺失日期的数据
- 避免重复下载，提高效率

### 2. 真实库存计算
- 自动计算真实库存 = FBA可售 + 调仓中 + 待调仓 + 待发货
- 提供更准确的库存分析

### 3. 断货曲线分析
生成三层交互式图表：
- **真实库存趋势**：显示库存变化和低库存警戒线
- **销量与库存变化**：对比销量和库存变化趋势
- **可售天数预警**：基于7日、14日、30日平均销量预测断货时间

### 4. Excel格式优化
- 自动设置列宽和数据格式
- 货币、百分比、日期等字段自动格式化
- 添加筛选器和冻结首行

## 注意事项

### 使用限制
1. **文件占用**：脚本运行时请勿打开相关Excel文件
2. **网络要求**：需要稳定的网络连接访问API
3. **日期限制**：结束日期不能是未来日期

### 错误处理
- 脚本包含完善的错误处理机制
- 数据转换使用安全函数避免类型错误
- API请求失败会显示详细错误信息

### 性能优化
- API请求间隔1秒，避免频率限制
- 按ASIN分组处理数据，提高处理效率
- 使用异步请求提高网络请求性能

## 输出文件说明

### JSON文件
- `length.json`：包含数据总数信息
- `{日期}_{店铺名}_产品表现.json`：每日的原始API数据

### Excel文件
- 按ASIN分组，每个ASIN一个Excel文件
- 文件名格式：`{ASIN}.xlsx`
- 数据按日期排序，包含完整的产品表现指标

### HTML文件
- 交互式断货曲线图
- 文件名格式：`{ASIN}.html`
- 支持缩放、悬停查看详细数据

## 常见问题

**Q: 如何获取店铺的SID？**
A: 首次运行脚本会自动获取店铺列表，然后使用选项1通过店铺名查询SID。

**Q: 数据更新频率如何？**
A: 脚本支持增量更新，只需重新运行并指定日期范围即可获取新数据。

**Q: 如何查看断货预警？**
A: 在生成的HTML图表中，红色虚线表示7天断货预警线，当预测天数低于此线时需要注意补货。

**Q: Excel文件打不开怎么办？**
A: 确保脚本运行时没有打开相关Excel文件，关闭文件后重新运行脚本。
